# WebSocket 服务

## 简介

基于 Midway.js 实现的简单高性能 WebSocket 服务，专注于文本消息的实时通信。

## 实现文件

### 核心文件（仅2个）

1. **WebSocket 控制器** - `src/socket/websocket.controller.ts`
   - 处理连接、消息、断开连接事件
   - 支持文本消息和心跳检测
   - 自动广播消息给所有连接的客户端

2. **WebSocket 服务** - `src/service/websocket.service.ts`
   - 提供消息广播功能
   - 客户端连接数统计

### 配置修改

- `src/configuration.ts` - 添加了 `@midwayjs/ws` 组件
- `src/config/config.default.ts` - 添加了 WebSocket 配置
- `package.json` - 添加了 `@midwayjs/ws` 依赖

## 功能特性

- ✅ WebSocket 连接管理
- ✅ 文本消息实时广播
- ✅ 心跳检测
- ✅ 内存存储（高性能）
- ✅ 简单易用

## 使用方式

### 启动服务
```bash
npm run dev
```

### 连接 WebSocket
```javascript
const ws = new WebSocket('ws://localhost:3001');
```

### 发送消息
```javascript
// 文本消息
ws.send(JSON.stringify({
  type: 'chat',
  content: 'Hello World!'
}));

// 心跳消息
ws.send(JSON.stringify({
  type: 'ping'
}));
```

### 接收消息
```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('收到消息:', data);
};
```

## 消息格式

### 连接成功消息
```json
{
  "type": "connected",
  "clientId": "client_1234567890_abc123",
  "message": "连接成功",
  "timestamp": 1640995200000
}
```

### 聊天消息
```json
{
  "type": "message",
  "clientId": "client_1234567890_abc123",
  "content": "Hello World!",
  "timestamp": 1640995200000
}
```

### 心跳响应
```json
{
  "type": "pong",
  "timestamp": 1640995200000
}
```

## 新订单广播功能

当用户下单并付款成功后，系统会自动通过 WebSocket 向所有连接的客户端广播新订单消息。

### 架构设计

- **统一广播服务** - `MessageBroadcastService` 统一处理所有消息广播
- **解耦设计** - 业务服务不直接依赖 WebSocket，通过消息广播服务间接调用
- **简化消息** - 只传递必要的标识信息，前端收到通知后自行查询详细数据

### 触发场景

1. **订单付款成功** - `OrderService.payOrder()`
2. **微信支付回调** - `PayCallbackController.handleServiceOrderCallback()`
3. **订单已支付异常处理** - `WepayService.jsapiForService()`

### 广播消息格式

```json
{
  "type": "new_order",
  "data": {
    "orderId": 123,
    "orderSn": "ORD20231225001",
    "serviceTypes": [
      {
        "serviceTypeId": 1,
        "serviceType": "XI_HU",
        "serviceTypeName": "洗护",
        "serviceName": "基础洗护"
      },
      {
        "serviceTypeId": 2,
        "serviceType": "MEI_RONG",
        "serviceTypeName": "美容",
        "serviceName": "造型美容"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

**字段说明**：
- `orderId`: 订单ID
- `orderSn`: 订单编号
- `serviceTypes`: 订单包含的服务类型数组
  - `serviceTypeId`: 服务类型ID
  - `serviceType`: 服务类型代码（如：XI_HU、MEI_RONG）
  - `serviceTypeName`: 服务类型名称
  - `serviceName`: 具体服务名称

### 客户端接收示例

```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);

  if (data.type === 'new_order') {
    console.log('收到新订单通知，订单ID:', data.data.orderId);
    console.log('订单编号:', data.data.orderSn);
    console.log('服务类型:', data.data.serviceTypes);

    // 根据员工职位判断是否显示该订单通知
    if (shouldShowOrderForEmployee(data.data.serviceTypes)) {
      // 前端收到通知后，自行调用API查询订单详情
      fetchOrderDetails(data.data.orderId);
      // 显示新订单提示
      showNewOrderNotification(data.data);
    }
  }
};

/**
 * 根据员工职位判断是否应该显示订单通知
 * @param {Array} serviceTypes 订单的服务类型数组
 * @returns {boolean} 是否应该显示
 */
function shouldShowOrderForEmployee(serviceTypes) {
  const employeePosition = getCurrentEmployeePosition(); // 获取当前员工职位

  // 职位与服务类型映射
  const positionServiceMapping = {
    'XI_HU_SHI': ['XI_HU'], // 洗护师只能接洗护
    'MEI_RONG_SHI': ['XI_HU', 'MEI_RONG'] // 美容师可以接洗护和美容
  };

  const allowedServiceTypes = positionServiceMapping[employeePosition] || [];

  // 检查订单中是否有该员工可以接的服务类型
  return serviceTypes.some(service =>
    allowedServiceTypes.includes(service.serviceType)
  );
}
```

### 取消订单广播

当订单被取消时，系统会自动广播取消订单消息。

#### 触发场景

1. **服务订单取消** - `OrderService.cancelOrder()`
2. **代金券订单取消** - `CouponOrderService.cancelOrder()`
3. **权益卡订单取消** - `MembershipCardOrderService.cancelOrder()`

#### 广播消息格式

```json
{
  "type": "cancel_order",
  "data": {
    "orderId": 123
  },
  "timestamp": 1640995200000
}
```

#### 客户端接收示例

```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);

  if (data.type === 'cancel_order') {
    console.log('收到取消订单通知，订单ID:', data.data.orderId);
    // 前端收到通知后，自行调用API查询订单详情或更新订单列表
    refreshOrderList();
    // 显示取消订单提示
    showCancelOrderNotification();
  }
};
```

### 其他支持的消息类型

```javascript
// 订单状态变更
{
  "type": "order_status_change",
  "data": {
    "orderId": 123,
    "status": "已接单"
  },
  "timestamp": 1640995200000
}

// 系统通知
{
  "type": "system_notification",
  "data": {
    "message": "系统维护通知",
    "level": "warning"
  },
  "timestamp": 1640995200000
}
```

## 特点

- **极简设计**：只有2个核心文件
- **高性能**：内存存储，无数据库依赖
- **实时广播**：所有消息自动广播给所有客户端
- **文本专用**：专注于文本消息，无文件传输等复杂功能
- **业务集成**：与订单系统深度集成，自动广播新订单
- **易于扩展**：基于 Midway.js 框架，易于集成和扩展
