# 员工职位WebSocket广播功能说明

## 功能概述

为了配合员工职位限制功能，WebSocket新订单广播消息现在包含订单的服务类型信息，客户端可以根据员工职位判断是否显示订单通知。

## 广播消息格式

### 新订单广播消息

```json
{
  "type": "new_order",
  "data": {
    "orderId": 123,
    "orderSn": "ORD20231225001",
    "serviceTypes": [
      {
        "serviceTypeId": 1,
        "serviceType": "XI_HU",
        "serviceTypeName": "洗护",
        "serviceName": "基础洗护"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| orderId | number | 订单ID |
| orderSn | string | 订单编号 |
| serviceTypes | array | 订单包含的服务类型数组 |
| serviceTypes[].serviceTypeId | number | 服务类型ID |
| serviceTypes[].serviceType | string | 服务类型代码（XI_HU/MEI_RONG） |
| serviceTypes[].serviceTypeName | string | 服务类型名称 |
| serviceTypes[].serviceName | string | 具体服务名称 |

## 客户端实现

### 基础接收逻辑

```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);

  if (data.type === 'new_order') {
    // 根据员工职位判断是否显示该订单通知
    if (shouldShowOrderForEmployee(data.data.serviceTypes)) {
      showNewOrderNotification(data.data);
    }
  }
};
```

### 职位过滤函数

```javascript
/**
 * 根据员工职位判断是否应该显示订单通知
 */
function shouldShowOrderForEmployee(serviceTypes) {
  const employeePosition = getCurrentEmployeePosition();
  
  // 职位与服务类型映射
  const positionServiceMapping = {
    'XI_HU_SHI': ['XI_HU'], // 洗护师只能接洗护
    'MEI_RONG_SHI': ['XI_HU', 'MEI_RONG'] // 美容师可以接洗护和美容
  };
  
  const allowedServiceTypes = positionServiceMapping[employeePosition] || [];
  
  // 检查订单中是否有该员工可以接的服务类型
  return serviceTypes.some(service => 
    allowedServiceTypes.includes(service.serviceType)
  );
}
```

### 完整示例

```javascript
class OrderNotificationHandler {
  constructor(employeePosition) {
    this.employeePosition = employeePosition;
    this.positionServiceMapping = {
      'XI_HU_SHI': ['XI_HU'],
      'MEI_RONG_SHI': ['XI_HU', 'MEI_RONG']
    };
  }

  handleWebSocketMessage(event) {
    const data = JSON.parse(event.data);

    switch (data.type) {
      case 'new_order':
        this.handleNewOrder(data.data);
        break;
      case 'cancel_order':
        this.handleCancelOrder(data.data);
        break;
    }
  }

  handleNewOrder(orderData) {
    if (this.shouldShowOrder(orderData.serviceTypes)) {
      this.showNotification({
        title: '新订单通知',
        message: `订单 ${orderData.orderSn} 等待接单`,
        serviceTypes: orderData.serviceTypes,
        orderId: orderData.orderId
      });
    }
  }

  shouldShowOrder(serviceTypes) {
    const allowedServiceTypes = this.positionServiceMapping[this.employeePosition] || [];
    
    return serviceTypes.some(service => 
      allowedServiceTypes.includes(service.serviceType)
    );
  }

  showNotification(data) {
    // 显示通知UI
    console.log('显示新订单通知:', data);
    
    // 可以根据服务类型显示不同的图标或颜色
    const serviceTypeNames = data.serviceTypes.map(st => st.serviceTypeName).join('、');
    
    // 更新UI
    this.updateOrderList();
    this.playNotificationSound();
    this.showToast(`新的${serviceTypeNames}订单`);
  }
}

// 使用示例
const handler = new OrderNotificationHandler('XI_HU_SHI'); // 洗护师
ws.onmessage = (event) => handler.handleWebSocketMessage(event);
```

## 业务场景

### 场景1：洗护师接收通知
- 员工职位：洗护师 (XI_HU_SHI)
- 订单服务类型：洗护 (XI_HU)
- 结果：显示通知 ✅

### 场景2：洗护师过滤美容订单
- 员工职位：洗护师 (XI_HU_SHI)
- 订单服务类型：美容 (MEI_RONG)
- 结果：不显示通知 ❌

### 场景3：美容师接收混合订单
- 员工职位：美容师 (MEI_RONG_SHI)
- 订单服务类型：洗护 + 美容 (XI_HU + MEI_RONG)
- 结果：显示通知 ✅

### 场景4：无职位限制
- 员工职位：未设置或其他
- 订单服务类型：任意
- 结果：显示通知 ✅（兼容性处理）

## 注意事项

1. **兼容性**：如果员工没有设置职位，默认显示所有订单通知
2. **多服务类型**：一个订单可能包含多个服务，只要有一个服务类型匹配就显示通知
3. **实时性**：WebSocket消息是实时的，客户端应该立即进行职位过滤判断
4. **错误处理**：如果消息格式异常，建议显示通知以避免遗漏订单
