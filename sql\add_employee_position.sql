-- 添加员工职位字段
-- 执行时间：2025-06-25

-- 1. 为员工表添加职位字段
ALTER TABLE employees ADD COLUMN position VARCHAR(50) COMMENT '职位';

-- 2. 为字典表添加员工职位数据
INSERT INTO dictionaries (type, code, name, sortOrder, status, createdAt, updatedAt) VALUES
('员工职位', 'XI_HU_SHI', '洗护师', 1, 1, NOW(), NOW()),
('员工职位', 'MEI_RONG_SHI', '美容师', 2, 1, NOW(), NOW());

-- 3. 可选：为现有员工设置默认职位（根据实际情况调整）
-- UPDATE employees SET position = 'MEI_RONG_SHI' WHERE position IS NULL;
