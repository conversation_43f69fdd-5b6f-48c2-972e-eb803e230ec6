import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { EmployeeService } from '../../service/employee.service';
import { Vehicle, ServiceType } from '../../entity';
import { POSITION_SERVICE_MAPPING } from '../../common/Constant';
import { CustomError } from '../../error/custom.error';
import { Op } from 'sequelize';

@Controller('/openapi/employees')
export class OpenApiEmployeeController {
  @Inject()
  service: EmployeeService;

  @Get('/', { summary: '获取可选择的员工列表（无需认证）' })
  async getAvailableEmployees(
    @Query('serviceTypeId') serviceTypeId?: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10
  ) {
    const offset = (current - 1) * pageSize;
    const limit = pageSize;

    // 构建查询条件
    const whereCondition: any = {
      status: 1, // 只查询启用的员工
    };

    // 如果指定了服务类型，需要根据员工职位筛选
    if (serviceTypeId) {
      // 验证服务类型是否存在并获取type
      const serviceType = await ServiceType.findByPk(serviceTypeId);
      if (!serviceType) {
        throw new CustomError('服务类型不存在');
      }

      // 根据服务类型筛选可以提供该服务的员工职位
      const allowedPositions: string[] = [];
      Object.entries(POSITION_SERVICE_MAPPING).forEach(
        ([position, serviceTypes]) => {
          if ((serviceTypes as string[]).includes(serviceType.type)) {
            allowedPositions.push(position);
          }
        }
      );

      if (allowedPositions.length > 0) {
        whereCondition.position = {
          [Op.in]: allowedPositions,
        };
      } else {
        // 如果没有员工职位可以提供该服务，返回空结果
        return {
          list: [],
          total: 0,
          current,
          pageSize,
        };
      }
    }

    // 构建include条件
    const includeConditions: any[] = [
      {
        model: Vehicle,
        required: false, // 左连接，允许员工没有车辆
      },
    ];

    const result = await this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      include: includeConditions,
      order: [
        ['rating', 'DESC'], // 按评分降序
        ['workExp', 'DESC'], // 按工作经验降序
        ['level', 'DESC'], // 按等级降序
      ],
    });

    // 只返回必要的字段
    const filteredList = result.list.map(employee => ({
      id: employee.id,
      name: employee.name,
      avatar: employee.avatar,
      level: employee.level,
      workExp: employee.workExp,
      rating: employee.rating,
      phone: employee.phone,
      vehicle: employee.vehicle
        ? {
            id: employee.vehicle.id,
            plateNumber: employee.vehicle.plateNumber,
            vehicleType: employee.vehicle.vehicleType,
            status: employee.vehicle.status,
          }
        : null,
    }));

    return {
      list: filteredList,
      total: result.total,
      current,
      pageSize,
    };
  }
}
