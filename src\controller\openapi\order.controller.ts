import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { OrderService } from '../../service/order.service';

@Controller('/openapi/orders')
export class OpenApiOrderController {
  @Inject()
  service: OrderService;

  @Get('/', { summary: '查询可接单列表（无需认证）' })
  async list(
    @Query('current') current: number,
    @Query('pageSize') pageSize: number,
    @Query('type') type: string,
    @Query('employeeId') employeeId?: number
  ) {
    const res = await this.service.findList({
      userId: 1, // 临时使用固定值
      page: current,
      pageSize,
      type,
      employeeId,
    });
    return res;
  }
}
