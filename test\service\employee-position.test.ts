import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/service/employee-position.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should validate employee position when accepting orders', async () => {
    // 测试洗护师不能接美容订单
    const result = await createHttpRequest(app)
      .post('/orders/1/accept')
      .send({ employeeId: 1 })
      .expect(400);
    
    // 这里应该返回职位限制的错误信息
    // 实际测试需要根据具体的测试数据进行调整
  });

  it('should allow employee to accept orders within their position scope', async () => {
    // 测试美容师可以接洗护和美容订单
    const result = await createHttpRequest(app)
      .post('/orders/2/accept')
      .send({ employeeId: 2 })
      .expect(200);
  });

  it('should filter orders by employee position in findList', async () => {
    // 测试可接单列表根据员工职位过滤（不指定type）
    const result1 = await createHttpRequest(app)
      .get('/orders/1')
      .expect(200);

    // 验证返回的订单都是该员工职位可以接的

    // 测试指定type且在员工职位允许范围内
    const result2 = await createHttpRequest(app)
      .get('/orders/1?type=XI_HU')
      .expect(200);

    // 测试指定type但不在员工职位允许范围内（假设员工1是洗护师）
    const result3 = await createHttpRequest(app)
      .get('/orders/1?type=MEI_RONG')
      .expect(200);

    // 应该返回空列表，因为洗护师不能接美容订单
    // expect(result3.body.data.list).toHaveLength(0);
  });
});
